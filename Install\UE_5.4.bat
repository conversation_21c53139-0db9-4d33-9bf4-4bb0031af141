@echo off
REM Check for administrative privileges
net session >nul 2>&1
if %errorLevel% == 0 (
    echo Success: Administrative privileges confirmed.
) else (
    echo Failure: Current permissions to execute this .BAT file are inadequate.
    echo Please run this script as an administrator.
    pause
    exit /b
)

echo Installation de Unreal Pagerender en cours...

REM Lancer l'installation
if exist "C:\Program Files\Pagerender\UE_5.4" rmdir /s /q "C:\Program Files\Pagerender\UE_5.4"
"C:\Program Files\7-Zip\7z.exe" x "\\cluster2019fs\INSTALL\_Programmes\Unreal\UE_5.4.7z" -o"C:\Program Files\Pagerender\"


:: Récupère le nom du premier utilisateur connecté (sans chevron)  
for /f "skip=1 tokens=1" %%u in ('query user') do (  
    set "REALUSER=%%u"  
    goto :break  
)  
:break  

:: Retire le chevron si présent  
set "REALUSER=%REALUSER:>=%"  
echo Utilisateur détecté : %REALUSER%  

:: C<PERSON>e le script batch à exécuter dans le contexte utilisateur  
set "USER_SCRIPT=C:\Program Files\Pagerender\UE_5.4\user_postinstall.bat"  
echo reg add "HKEY_CURRENT_USER\Software\Epic Games\Unreal Engine\Builds" /v "UE_5.4" /t REG_SZ /d "C:\Program Files\Pagerender\UE_5.4" /f > "%USER_SCRIPT%"
echo powershell -Command "$s = (New-Object -COM WScript.Shell).CreateShortcut(\"$env:USERPROFILE\Desktop\UE_5.4.lnk\"); $s.TargetPath = 'C:\Program Files\Pagerender\UE_5.4\Engine\Binaries\Win64\UnrealEditor.exe'; $s.Save()" >> "%USER_SCRIPT%"


:: Crée la tâche planifiée pour l'utilisateur détecté  
schtasks /Create /F /SC ONCE /TN "UE54_UserPostInstall" /TR "\"%USER_SCRIPT%\"" /ST 23:59 /RL LIMITED /RU "%REALUSER%"  
  
:: Lance la tâche immédiatement  
schtasks /Run /TN "UE54_UserPostInstall"

:: Supprime la tâche
schtasks /Delete /TN "UE54_UserPostInstall" /f


REM Verifier si l'installation s'est bien déroulee
if %errorlevel% equ 0 (
    echo Installation de Unreal terminee avec succes.
) else (
    echo Une erreur est survenue lors de l'installation.
)

timeout /t 4