[EditorLayouts]
UnrealEd_Layout_v1.5=("Type": "Layout","Name": "UnrealEd_Layout_v1.5","PrimaryAreaIndex": 0,"Areas": [("SizeCoefficient": 1,"Type": "Area","Orientation": "Orient_Horizontal","WindowPlacement": "Placement_NoWindow","Nodes": [("SizeCoefficient": 2,"Type": "Stack","HideTabWell": false,"ForegroundTab": "LevelEditor","Tabs": [("TabId": "LevelEditor","TabState": "OpenedTab"),("TabId": "DockedToolkit","TabState": "ClosedTab")])]),("SizeCoefficient": 1,"Type": "Area","Orientation": "Orient_Vertical","WindowPlacement": "Placement_Automatic","WindowSize_X": 1664,"WindowSize_Y": 910,"Nodes": [("SizeCoefficient": 1,"Type": "Stack","HideTabWell": false,"ForegroundTab": "None","Tabs": [("TabId": "StandaloneToolkit","TabState": "ClosedTab")])]),("SizeCoefficient": 1,"Type": "Area","Orientation": "Orient_Horizontal","WindowPlacement": "Placement_Specified","WindowPosition_X": 125,"WindowPosition_Y": 62,"WindowSize_X": 1670,"WindowSize_Y": 916,"bIsMaximized": false,"Nodes": [("SizeCoefficient": 1,"Type": "Stack","HideTabWell": false,"ForegroundTab": "None","Tabs": [("TabId": "EditorSettings","TabState": "ClosedTab"),("TabId": "ProjectSettings","TabState": "ClosedTab"),("TabId": "PluginsEditor","TabState": "ClosedTab")])])])
LevelEditor_Layout_v1.8=("Type": "Layout","Name": "LevelEditor_Layout_v1.8","PrimaryAreaIndex": 0,"Areas": [("SizeCoefficient": 1,"Type": "Area","Orientation": "Orient_Horizontal","WindowPlacement": "Placement_NoWindow","Nodes": [("SizeCoefficient": 0.93510723114013672,"Type": "Splitter","Orientation": "Orient_Vertical","Nodes": [("SizeCoefficient": 0.75,"Type": "Splitter","Orientation": "Orient_Horizontal","Nodes": [("SizeCoefficient": 0.15000000596046448,"Type": "Stack","HideTabWell": true,"ForegroundTab": "None","Tabs": [("TabId": "VerticalModeToolbar","TabState": "ClosedTab")]),("SizeCoefficient": 0.30000001192092896,"Type": "Splitter","Orientation": "Orient_Vertical","Nodes": [("SizeCoefficient": 0.5,"Type": "Stack","HideTabWell": false,"ForegroundTab": "PlacementBrowser","Tabs": [("TabId": "TopLeftModeTab","TabState": "ClosedTab"),("TabId": "PlacementBrowser","TabState": "OpenedTab")]),("SizeCoefficient": 0.5,"Type": "Stack","HideTabWell": false,"ForegroundTab": "None","Tabs": [("TabId": "BottomLeftModeTab","TabState": "ClosedTab")])]),("SizeCoefficient": 1,"Type": "Stack","HideTabWell": true,"ForegroundTab": "LevelEditorViewport","Tabs": [("TabId": "LevelEditorViewport","TabState": "OpenedTab")])]),("SizeCoefficient": 0.40000000596046448,"Type": "Stack","HideTabWell": false,"ForegroundTab": "None","Tabs": [("TabId": "EmbeddedSequenceID","TabState": "ClosedTab"),("TabId": "ContentBrowserTab1","TabState": "ClosedTab"),("TabId": "Sequencer","TabState": "ClosedTab"),("TabId": "OutputLog","TabState": "ClosedTab")])]),("SizeCoefficient": 0.30000001192092896,"Type": "Stack","HideTabWell": false,"ForegroundTab": "None","Tabs": [("TabId": "TakeRecorder","TabState": "ClosedTab")]),("SizeCoefficient": 0.31489259004592896,"Type": "Splitter","Orientation": "Orient_Vertical","Nodes": [("SizeCoefficient": 0.58563530445098877,"Type": "Stack","HideTabWell": false,"ForegroundTab": "LightForge","Tabs": [("TabId": "TopRightModeTab","TabState": "ClosedTab"),("TabId": "LightForge","TabState": "OpenedTab"),("TabId": "LevelEditorSceneOutliner","TabState": "OpenedTab"),("TabId": "LevelEditorLayerBrowser","TabState": "ClosedTab")]),("SizeCoefficient": 0.81436485052108765,"Type": "Stack","HideTabWell": false,"ForegroundTab": "LevelEditorSelectionDetails","Tabs": [("TabId": "BottomRightModeTab","TabState": "ClosedTab"),("TabId": "LevelEditorSelectionDetails","TabState": "OpenedTab"),("TabId": "WorldBrowserPartitionEditor","TabState": "ClosedTab"),("TabId": "WorldSettingsTab","TabState": "ClosedTab")])])])])
LayoutName=NSLOCTEXT("LayoutNamespace", "LightForge_Default_Editor_Layout", "LightForge Default Editor Layout")
LayoutDescription=NSLOCTEXT("LayoutNamespace", "Default_Unreal_Engine_5_layout_with_LightForge_window", "Default Unreal Engine 5 layout with LightForge window")

