// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Modules/ModuleManager.h"
#include "RenderLoggerPrivatePCH.h"

// Déclaration anticipée pour éviter les inclusions inutiles
class UMoviePipeline;

/**
 * Module de logging pour les rendus Unreal
 * Enregistre les informations de rendu dans des fichiers CSV pour analyse ultérieure
 */
class RENDERLOGGER_API FRenderLoggerModule : public IModuleInterface
{
public:
    /** IModuleInterface implementation */
    virtual void StartupModule() override;
    virtual void ShutdownModule() override;

private:
    void OnMoviePipelineFinished(UMoviePipeline* MoviePipeline);
    void WriteRenderLog(const FString& UserName, const FString& ProjectName, const FString& SequenceName, int32 NumFrames, float Duration);
    
    FDelegateHandle OnMoviePipelineFinishedHandle;
};
