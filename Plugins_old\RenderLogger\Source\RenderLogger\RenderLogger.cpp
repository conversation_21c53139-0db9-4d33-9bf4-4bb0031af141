// Copyright EpicGames, Inc. All Rights Reserved.

#include "RenderLogger.h"
#include "HAL/PlatformProcess.h"
#include "Misc/Paths.h"
#include "Misc/FileHelper.h"
#include "Misc/DateTime.h"
#include "HAL/PlatformMisc.h"
#include "Misc/App.h"

// Inclure les en-têtes spécifiques à l'éditeur
#if WITH_EDITOR
#include "Editor.h"
#include "MoviePipelineQueueSubsystem.h"
#include "MoviePipeline.h"
#include "MoviePipelineOutputSetting.h"
#include "MoviePipelinePrimaryConfig.h"
#include "LevelSequence.h"
#endif

#define LOCTEXT_NAMESPACE "FRenderLoggerModule"

void FRenderLoggerModule::StartupModule()
{
    // Ne s'exécute que dans l'éditeur
    if (!GIsEditor)
    {
        UE_LOG(LogTemp, Warning, TEXT("RenderLogger: Plugin désactivé en dehors de l'éditeur"));
        return;
    }

#if WITH_EDITOR
    // S'abonner uniquement si nous sommes dans l'éditeur
    if (GEditor)
    {
        UMoviePipelineQueueSubsystem* QueueSubsystem = GEditor->GetEngineSubsystem<UMoviePipelineQueueSubsystem>();
        if (QueueSubsystem)
        {
            OnMoviePipelineFinishedHandle = QueueSubsystem->OnRenderFinished().AddRaw(this, &FRenderLoggerModule::OnMoviePipelineFinished);
            UE_LOG(LogTemp, Log, TEXT("RenderLogger: Module démarré avec succès"));
        }
        else
        {
            UE_LOG(LogTemp, Warning, TEXT("RenderLogger: Impossible de récupérer le MoviePipelineQueueSubsystem"));
        }
    }
#endif
}

void FRenderLoggerModule::ShutdownModule()
{
#if WITH_EDITOR
    // Seulement si nous sommes dans l'éditeur
    if (GEditor)
    {
        UMoviePipelineQueueSubsystem* QueueSubsystem = GEditor->GetEngineSubsystem<UMoviePipelineQueueSubsystem>();
        if (QueueSubsystem && OnMoviePipelineFinishedHandle.IsValid())
        {
            QueueSubsystem->OnRenderFinished().Remove(OnMoviePipelineFinishedHandle);
            OnMoviePipelineFinishedHandle.Reset();
        }
    }
#endif
}

void FRenderLoggerModule::OnMoviePipelineFinished(UMoviePipeline* MoviePipeline)
{
    if (!MoviePipeline || !IsInGameThread())
    {
        UE_LOG(LogTemp, Warning, TEXT("RenderLogger: Pipeline invalide ou appelé depuis un thread incorrect"));
        return;
    }

    // Get basic information
    FString UserName = FPlatformMisc::GetEnvironmentVariable(TEXT("USERNAME"));
    if (UserName.IsEmpty())
    {
        UserName = FPlatformMisc::GetEnvironmentVariable(TEXT("USER"));
    }

    FString ProjectName = FApp::GetProjectName();
    
    FString SequenceName = TEXT("Unknown");
    if (MoviePipeline->GetTargetSequence())
    {
        SequenceName = MoviePipeline->GetTargetSequence()->GetName();
    }

    // Calculate duration
    float Duration = 0.0f;
    if (MoviePipeline->GetPipelineStartTime() > 0 && MoviePipeline->GetPipelineEndTime() > 0)
    {
        Duration = MoviePipeline->GetPipelineEndTime() - MoviePipeline->GetPipelineStartTime();
    }

    // Get number of frames rendered
    int32 NumFrames = 0;
    if (UMoviePipelineOutputSetting* OutputSettings = MoviePipeline->FindOrAddSetting<UMoviePipelineOutputSetting>(MoviePipeline->GetPipelinePrimaryConfig()))
    {
        FIntPoint OutputResolution = OutputSettings->OutputResolution;
        FString OutputDirectory = OutputSettings->OutputDirectory.Path;
        
        // Count rendered frames in output directory
        TArray<FString> FoundFiles;
        IFileManager::Get().FindFiles(FoundFiles, *FPaths::Combine(OutputDirectory, TEXT("*.png")), true, false);
        IFileManager::Get().FindFiles(FoundFiles, *FPaths::Combine(OutputDirectory, TEXT("*.jpg")), true, true);
        IFileManager::Get().FindFiles(FoundFiles, *FPaths::Combine(OutputDirectory, TEXT("*.exr")), true, true);
        
        NumFrames = FoundFiles.Num();
    }

    // Write the log
    WriteRenderLog(UserName, ProjectName, SequenceName, NumFrames, Duration);
}

void FRenderLoggerModule::WriteRenderLog(const FString& UserName, const FString& ProjectName, const FString& SequenceName, int32 NumFrames, float Duration)
{
    // Create log directory if it doesn't exist
    FString LogDir = TEXT("X:\\workspace_Unreal\\render_logs");
    IPlatformFile& PlatformFile = FPlatformFileManager::Get().GetPlatformFile();
    if (!PlatformFile.DirectoryExists(*LogDir))
    {
        PlatformFile.CreateDirectoryTree(*LogDir);
    }

    // Create log file name with current year and month
    FDateTime Now = FDateTime::Now();
    FString LogFileName = FString::Printf(TEXT("unreal_render_log_%04d_%02d.csv"), Now.GetYear(), Now.GetMonth());
    FString LogFilePath = FPaths::Combine(LogDir, LogFileName);

    // Check if file exists to write header
    bool bFileExists = FPaths::FileExists(LogFilePath);
    
    FString LogLine = FString::Printf(
        TEXT("%s;%s;%s;%s;%d;%.2f\n"),
        *Now.ToIso8601(),
        *UserName,
        *ProjectName,
        *SequenceName,
        NumFrames,
        Duration
    );

    // Write to file
    FFileHelper::SaveStringToFile(LogLine, *LogFilePath, FFileHelper::EEncodingOptions::AutoDetect, &IFileManager::Get(), EFileWrite::FILEWRITE_Append);
    
    // If file didn't exist, write UTF-8 BOM and header
    if (!bFileExists)
    {
        FString Header = TEXT("Timestamp;User;Project;Sequence;FramesRendered;Duration(seconds)\n");
        FFileHelper::SaveStringToFile(Header, *LogFilePath, FFileHelper::EEncodingOptions::ForceUTF8, &IFileManager::Get(), EFileWrite::FILEWRITE_NoFail);
        FFileHelper::SaveStringToFile(LogLine, *LogFilePath, FFileHelper::EEncodingOptions::AutoDetect, &IFileManager::Get(), EFileWrite::FILEWRITE_Append);
    }
}

#undef LOCTEXT_NAMESPACE
    
IMPLEMENT_MODULE(FRenderLoggerModule, RenderLogger)
