@echo off
setlocal

set ENGINE_DIR=D:\EpicGames\UnrealEngine
set PLUGIN_DIR=%~dp0

REM Nettoyer les fichiers intermédiaires
if exist "%ENGINE_DIR%\Engine\Intermediate" rmdir /s /q "%ENGINE_DIR%\Engine\Intermediate"
if exist "%ENGINE_DIR%\Engine\Binaries" rmdir /s /q "%ENGINE_DIR%\Engine\Binaries"

REM Générer les fichiers du projet
echo Génération des fichiers du projet...
call "%ENGINE_DIR%\Engine\Build\BatchFiles\Build.bat" -ProjectFiles -project="%PLUGIN_DIR%" -game -rocket -progress -log="%PLUGIN_DIR%\GenerateProjectFiles.log"

REM Compiler le plugin
echo Compilation du plugin...
call "%ENGINE_DIR%\Engine\Build\BatchFiles\Build.bat" RenderLogger Win64 Development -Plugin="%PLUGIN_DIR%RenderLogger.uplugin"

pause
