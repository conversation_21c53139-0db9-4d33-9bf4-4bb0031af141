import unreal

def select_objects_with_same_material():
    editor_level_lib = unreal.EditorLevelLibrary()
    selected_actors = editor_level_lib.get_selected_level_actors()
    
    if not selected_actors:
        unreal.log_warning("Aucun objet sélectionné !")
        return
    
    source_actor = selected_actors[0]
    static_mesh_component = source_actor.get_component_by_class(unreal.StaticMeshComponent)
    
    if not static_mesh_component:
        unreal.log_warning("L'objet sélectionné n'a pas de composant de maillage statique !")
        return
    
    materials = static_mesh_component.get_materials()
    if not materials:
        unreal.log_warning("L'objet sélectionné n'a pas de matériau assigné !")
        return
    
    editor_level_lib.select_nothing()
    all_actors = unreal.GameplayStatics.get_all_actors_of_class(
        unreal.EditorLevelLibrary.get_editor_world(), 
        unreal.StaticMeshActor
    )
    
    actors_with_same_material = [
        actor for actor in all_actors
        if (comp := actor.get_component_by_class(unreal.StaticMeshComponent))
        and any(mat in comp.get_materials() for mat in materials)
    ]
    
    if actors_with_same_material:
        editor_level_lib.set_selected_level_actors(actors_with_same_material)
        unreal.log(f"{len(actors_with_same_material)} objets avec le même matériau ont été sélectionnés.")
    else:
        unreal.log("Aucun autre objet avec le même matériau n'a été trouvé.")

def register_tools(menu):
    # Créer ou récupérer la section
    section_name = "MaterialToolsSection"
    section_label = "Outils Matériaux"
    
    # Créer directement une nouvelle section (l'API d'Unreal Engine 5.6 gère les sections dupliquées)
    menu.add_section(section_name, section_label)
    
    # Créer l'entrée de menu
    entry = unreal.ToolMenuEntry(
        name="SelectObjectsWithSameMaterial",
        type=unreal.MultiBlockType.MENU_ENTRY
    )
    entry.set_label("Sélectionner objets avec le même matériau")
    entry.set_tool_tip("Sélectionne tous les objets ayant le même matériau que la sélection actuelle")
    entry.set_icon("EditorStyle", "MaterialEditor.ApplyMaterial")
    entry.set_string_command(
        unreal.ToolMenuStringCommandType.PYTHON,
        "Script",
        "from material_tools import select_objects_with_same_material; select_objects_with_same_material()"
    )
    
    # Ajouter l'entrée directement au menu en spécifiant la section
    menu.add_menu_entry(section_name, entry)
    unreal.log("Entrée de menu 'Sélectionner par Matériau' ajoutée")
