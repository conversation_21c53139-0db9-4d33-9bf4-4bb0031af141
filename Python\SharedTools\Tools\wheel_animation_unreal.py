"""
Script d'animation automatique des roues pour véhicules USD dans Unreal Engine

Ce script génère automatiquement l'animation des roues basée sur le déplacement du véhicule
dans un environnement USD (Universal Scene Description) avec le Stage Editor d'Unreal Engine.

Fonctionnalités:
- Animation du roulement des roues (rotation pitch) basée sur la distance parcourue
- Animation du braquage des freins avant (rotation yaw) basée sur le changement de direction
- Support de la hiérarchie USD avec roots "heros" ou "fantome#"
- Intégration avec les tracks de sequencer existants
- Validation automatique de la configuration

Utilisation:
1. Ouvrir un séquenceur avec des tracks pour les roues et le véhicule
2. Sélectionner les tracks suivants dans le sequencer:
   - Tracks des roues: Roue_AV_G, Roue_AV_D, Roue_AR_G, Roue_AR_D
   - Tracks des freins (optionnel): Frein_AV_G, Frein_AV_D
   - Track de mouvement: heros, fantome#, ou Deplacement
3. Exécuter le script via le menu ou directement

Paramètres ajustables:
- wheel_radius: Rayon des roues en cm (défaut: 30.0)
- steering_factor: Facteur de braquage (défaut: 2.0)
- max_steering_angle: Angle de braquage maximum en degrés (défaut: 45.0)

Auteur: Adapté pour USD à partir du script Maya original
Version: 2.0 - Support USD
"""

import unreal
import math

def get_usd_stage_actor():
    """
    Get the USD Stage Actor from the current level.
    """
    editor_actor_subsystem = unreal.get_editor_subsystem(unreal.EditorActorSubsystem)
    actors = editor_actor_subsystem.get_all_level_actors()

    for actor in actors:
        if actor.get_class().get_name() == "UsdStageActor":
            return actor
    return None

def get_selected_tracks():
    """
    Get selected tracks from sequencer using multiple methods.
    """
    selected_tracks = []

    try:
        # Méthode 1: API standard
        tracks = unreal.LevelSequenceEditorBlueprintLibrary.get_selected_tracks()
        if tracks:
            selected_tracks.extend(tracks)
            unreal.log(f"Méthode 1: Trouvé {len(tracks)} tracks")
    except Exception as e:
        unreal.log(f"Méthode 1 échouée: {e}")

    try:
        # Méthode 2: Via le subsystem
        sequencer_subsystem = unreal.get_editor_subsystem(unreal.LevelSequenceEditorSubsystem)
        if sequencer_subsystem:
            # Cette méthode peut nécessiter une approche différente
            unreal.log("Subsystem sequencer trouvé")
    except Exception as e:
        unreal.log(f"Méthode 2 échouée: {e}")

    return selected_tracks

def get_all_tracks_from_sequencer():
    """
    Get all tracks from the current sequencer as fallback.
    """
    try:
        sequencer = unreal.LevelSequenceEditorBlueprintLibrary.get_current_level_sequence()
        if not sequencer:
            return []

        # Obtenir tous les tracks de la séquence
        master_tracks = sequencer.get_master_tracks()
        possessable_tracks = []

        # Parcourir les possessables (objets animés)
        for i in range(sequencer.get_possessable_count()):
            possessable = sequencer.get_possessable(i)
            tracks = sequencer.find_tracks_by_type(possessable.get_possessable_guid(), unreal.MovieSceneTransformTrack)
            possessable_tracks.extend(tracks)

        all_tracks = master_tracks + possessable_tracks
        unreal.log(f"Tous les tracks trouvés: {len(all_tracks)}")

        return all_tracks

    except Exception as e:
        unreal.log_error(f"Erreur lors de la récupération des tracks: {e}")
        return []

def find_car_root_from_wheel(wheel_prim_path):
    """
    Find the car root (heros or fantome#) from a wheel prim path.
    """
    # Remonter la hiérarchie pour trouver le root de la voiture
    path_parts = wheel_prim_path.split('/')

    for i, part in enumerate(path_parts):
        if part.startswith('heros') or part.startswith('fantome'):
            # Construire le chemin vers le root de la voiture
            return '/'.join(path_parts[:i+1])

    return None

def find_movement_prim(car_root_path):
    """
    Find the movement prim (either the root itself or 'Deplacement').
    """
    # D'abord essayer le root lui-même
    movement_candidates = [
        car_root_path,  # Le root directement
        f"{car_root_path}/Deplacement"  # Ou l'objet Deplacement
    ]

    return movement_candidates

def validate_track_channels(track, required_channels=6):
    """
    Validate that a track has the required number of channels.
    """
    try:
        sections = track.get_sections()
        if not sections:
            return False

        channels = sections[0].get_all_channels()
        return len(channels) >= required_channels
    except:
        return False

def wheel_bake():
    """
    Animate car wheels based on movement in USD hierarchy.
    Works with selected tracks in Sequencer for USD prims.
    """
    try:
        # Get the current sequencer
        sequencer = unreal.LevelSequenceEditorBlueprintLibrary.get_current_level_sequence()
        if not sequencer:
            unreal.log_warning("Aucun séquenceur n'est ouvert. Ouvrez un séquenceur et réessayez.")
            return 0

        # Get selected tracks (should be wheel tracks: Roue_AV_G, Roue_AV_D, etc.)
        selected_tracks = get_selected_tracks()

        if not selected_tracks:
            unreal.log_warning("Aucun track sélectionné via l'API standard.")
            unreal.log("Tentative de récupération de tous les tracks...")

            # Fallback: utiliser tous les tracks et filtrer par nom
            all_tracks = get_all_tracks_from_sequencer()
            if not all_tracks:
                unreal.log_warning("Impossible de récupérer les tracks du sequencer.")
                return 0

            # Filtrer les tracks par nom pour trouver ceux qui nous intéressent
            selected_tracks = []
            for track in all_tracks:
                try:
                    track_name = track.get_display_name().lower()
                    if any(keyword in track_name for keyword in ["roue_", "frein_", "heros", "fantome", "deplacement"]):
                        selected_tracks.append(track)
                        unreal.log(f"Track trouvé: {track.get_display_name()}")
                except:
                    continue

            if not selected_tracks:
                unreal.log_warning("Aucun track de roue, frein ou véhicule trouvé dans le sequencer.")
                unreal.log("Assurez-vous que vos tracks contiennent 'roue_', 'frein_', 'heros', 'fantome' ou 'deplacement' dans leur nom.")
                return 0

            unreal.log(f"Mode automatique: {len(selected_tracks)} tracks trouvés automatiquement.")

        # Get animation range
        start_frame = sequencer.get_playback_start()
        end_frame = sequencer.get_playback_end()

        # Analyze selected tracks to find wheels and brakes
        wheel_tracks = []
        brake_tracks = []
        movement_track = None

        for track in selected_tracks:
            track_name = track.get_display_name()

            if "roue_" in track_name.lower():
                wheel_tracks.append(track)
            elif "frein_av_" in track_name.lower():
                brake_tracks.append(track)
            elif any(name in track_name.lower() for name in ["heros", "fantome", "deplacement"]):
                movement_track = track

        if not wheel_tracks and not brake_tracks:
            unreal.log_warning("Aucun track de roue ou frein trouvé. Sélectionnez les tracks Roue_AV_G, Frein_AV_D, etc.")
            return 0

        if not movement_track:
            unreal.log_warning("Aucun track de mouvement trouvé. Sélectionnez aussi le track du véhicule (heros, fantome ou Deplacement).")
            return 0

        # Validate tracks have the required channels
        if not validate_track_channels(movement_track):
            unreal.log_warning("Le track de mouvement n'a pas assez de canaux (6 requis: X,Y,Z translation + X,Y,Z rotation).")
            return 0

        for wheel_track in wheel_tracks:
            if not validate_track_channels(wheel_track):
                unreal.log_warning(f"Le track de roue '{wheel_track.get_display_name()}' n'a pas assez de canaux.")
                return 0

        for brake_track in brake_tracks:
            if not validate_track_channels(brake_track):
                unreal.log_warning(f"Le track de frein '{brake_track.get_display_name()}' n'a pas assez de canaux.")
                return 0
        
        # Animation parameters
        wheel_radius = 30.0  # Ajustez selon la taille de vos roues (en cm)
        wheel_circumference = 2 * math.pi * wheel_radius

        # Get movement track section and channels
        movement_section = movement_track.get_sections()[0]
        movement_channels = movement_section.get_all_channels()

        # Channels: [0,1,2] = Translation X,Y,Z, [3,4,5] = Rotation X,Y,Z
        translation_x_channel = movement_channels[0]
        translation_y_channel = movement_channels[1]
        translation_z_channel = movement_channels[2]

        # Calculate movement and rotations for each frame
        previous_position = None
        previous_direction = None
        accumulated_wheel_rotation = 0.0

        # Animation loop
        for frame in range(int(start_frame), int(end_frame) + 1):
            frame_number = unreal.FrameNumber(frame)

            # Get current position from movement track
            current_x = translation_x_channel.evaluate_keys(frame_number)
            current_y = translation_y_channel.evaluate_keys(frame_number)
            current_z = translation_z_channel.evaluate_keys(frame_number)
            current_position = unreal.Vector(current_x, current_y, current_z)

            if previous_position is not None:
                # Calculate movement distance and direction
                movement_vector = current_position - previous_position
                movement_distance = movement_vector.length()

                if movement_distance > 0.01:  # Éviter les divisions par zéro
                    current_direction = movement_vector.get_safe_normal()

                    # Calculate wheel rotation (pitch) based on movement
                    # Formula: distance / circumference * 360 degrees
                    wheel_rotation_increment = (movement_distance / wheel_circumference) * 360.0
                    accumulated_wheel_rotation += wheel_rotation_increment

                    # Apply wheel rotation to all wheel tracks
                    for wheel_track in wheel_tracks:
                        wheel_section = wheel_track.get_sections()[0]
                        wheel_channels = wheel_section.get_all_channels()

                        # Channel 4 is pitch rotation (Y-axis)
                        pitch_channel = wheel_channels[4]
                        pitch_channel.add_key(frame_number, accumulated_wheel_rotation)

                    # Calculate steering angle for brake tracks (front wheels)
                    if brake_tracks and previous_direction is not None:
                        # Calculer l'angle de braquage basé sur le changement de direction
                        dot_product = current_direction.dot(previous_direction)
                        # Limiter le dot product pour éviter les erreurs d'arrondi
                        dot_product = max(-1.0, min(1.0, dot_product))

                        # Calculer l'angle de changement de direction
                        direction_change_angle = math.acos(dot_product) * 180.0 / math.pi

                        # Déterminer le sens du braquage (gauche/droite)
                        cross_product = current_direction.cross(previous_direction)
                        steering_direction = 1.0 if cross_product.z > 0 else -1.0

                        # Appliquer un facteur de braquage (ajustable)
                        steering_factor = 2.0  # Augmentez pour plus de braquage
                        steering_angle = direction_change_angle * steering_direction * steering_factor

                        # Limiter l'angle de braquage (par exemple ±45 degrés)
                        max_steering_angle = 45.0
                        steering_angle = max(-max_steering_angle, min(max_steering_angle, steering_angle))

                        for brake_track in brake_tracks:
                            brake_section = brake_track.get_sections()[0]
                            brake_channels = brake_section.get_all_channels()

                            # Channel 5 is yaw rotation (Z-axis) for steering
                            yaw_channel = brake_channels[5]

                            # Appliquer le braquage différentiel (roue intérieure/extérieure)
                            track_name = brake_track.get_display_name().lower()
                            if "frein_av_g" in track_name:  # Roue gauche
                                final_steering = steering_angle
                            elif "frein_av_d" in track_name:  # Roue droite
                                final_steering = steering_angle
                            else:
                                final_steering = steering_angle

                            yaw_channel.add_key(frame_number, final_steering)

                    previous_direction = current_direction

            previous_position = current_position

        unreal.log("Animation des roues terminée avec succès !")
        unreal.log(f"Roues animées: {len(wheel_tracks)}")
        unreal.log(f"Freins animés: {len(brake_tracks)}")
        unreal.log(f"Frames traitées: {int(end_frame) - int(start_frame) + 1}")
        unreal.log(f"Rotation totale des roues: {accumulated_wheel_rotation:.2f} degrés")
        return 1

    except Exception as e:
        unreal.log_error(f"Erreur lors de l'animation des roues : {str(e)}")
        import traceback
        unreal.log_error(f"Détails de l'erreur: {traceback.format_exc()}")
        return 0

def test_wheel_animation():
    """
    Test function to validate the wheel animation setup.
    """
    try:
        # Check if sequencer is open
        sequencer = unreal.LevelSequenceEditorBlueprintLibrary.get_current_level_sequence()
        if not sequencer:
            unreal.log_warning("TEST: Aucun séquenceur ouvert")
            return False

        # Check selected tracks
        selected_tracks = unreal.LevelSequenceEditorBlueprintLibrary.get_selected_tracks()
        if not selected_tracks:
            unreal.log_warning("TEST: Aucun track sélectionné")
            return False

        unreal.log(f"TEST: {len(selected_tracks)} tracks sélectionnés:")
        for track in selected_tracks:
            track_name = track.get_display_name()
            has_channels = validate_track_channels(track)
            unreal.log(f"  - {track_name} (Canaux valides: {has_channels})")

        # Analyze track types
        wheel_count = sum(1 for track in selected_tracks if "roue_" in track.get_display_name().lower())
        brake_count = sum(1 for track in selected_tracks if "frein_av_" in track.get_display_name().lower())
        movement_count = sum(1 for track in selected_tracks if any(name in track.get_display_name().lower() for name in ["heros", "fantome", "deplacement"]))

        unreal.log(f"TEST: Tracks détectés - Roues: {wheel_count}, Freins: {brake_count}, Mouvement: {movement_count}")

        if wheel_count == 0 and brake_count == 0:
            unreal.log_warning("TEST: Aucun track de roue ou frein détecté")
            return False

        if movement_count == 0:
            unreal.log_warning("TEST: Aucun track de mouvement détecté")
            return False

        unreal.log("TEST: Configuration valide pour l'animation des roues")
        return True

    except Exception as e:
        unreal.log_error(f"TEST: Erreur lors du test : {str(e)}")
        return False

def register_tools(menu):
    """Register wheel animation tools in the menu"""
    section_name = "WheelAnimationSection"
    section_label = "Animation Roues USD"

    # Créer directement une nouvelle section (l'API d'Unreal Engine 5.6 gère les sections dupliquées)
    menu.add_section(section_name, section_label)

    # Create main animation entry
    entry_animate = unreal.ToolMenuEntry(
        name="WheelBakeUSD",
        type=unreal.MultiBlockType.MENU_ENTRY
    )
    entry_animate.set_label("Animer les roues USD")
    entry_animate.set_tool_tip("Génère l'animation des roues basée sur le déplacement du véhicule USD. Sélectionnez les tracks des roues et du véhicule dans le sequencer.")
    entry_animate.set_icon("EditorStyle", "Matinee.Pan.Translate")
    entry_animate.set_string_command(
        unreal.ToolMenuStringCommandType.PYTHON,
        "Script",
        "from wheel_animation_unreal import wheel_bake; wheel_bake()"
    )

    # Create test entry
    entry_test = unreal.ToolMenuEntry(
        name="WheelTestUSD",
        type=unreal.MultiBlockType.MENU_ENTRY
    )
    entry_test.set_label("Tester configuration roues")
    entry_test.set_tool_tip("Teste la configuration actuelle pour l'animation des roues USD")
    entry_test.set_icon("EditorStyle", "Matinee.ToggleRecordInterpValues")
    entry_test.set_string_command(
        unreal.ToolMenuStringCommandType.PYTHON,
        "Script",
        "from wheel_animation_unreal import test_wheel_animation; test_wheel_animation()"
    )

    # Ajouter les entrées au menu
    menu.add_menu_entry(section_name, entry_animate)
    menu.add_menu_entry(section_name, entry_test)
    unreal.log("Entrées de menu 'Animation des Roues USD' ajoutées")

def show_usage_instructions():
    """
    Display usage instructions for the wheel animation script.
    """
    instructions = """
=== INSTRUCTIONS D'UTILISATION - Animation Roues USD ===

1. PRÉPARATION:
   - Ouvrir un séquenceur avec votre scène USD
   - Vérifier que votre hiérarchie contient:
     * Un root de véhicule (heros, fantome#, ou Deplacement)
     * Des roues (Roue_AV_G, Roue_AV_D, Roue_AR_G, Roue_AR_D)
     * Des freins avant (Frein_AV_G, Frein_AV_D) - optionnel

2. SÉLECTION DES TRACKS:
   - Dans le sequencer, sélectionner les tracks suivants:
     ✓ Track de mouvement du véhicule (heros/fantome/Deplacement)
     ✓ Tracks des roues que vous voulez animer
     ✓ Tracks des freins avant (pour le braquage)

3. EXÉCUTION:
   - Menu: Tools > Animation Roues USD > Animer les roues USD
   - Ou: Tools > Animation Roues USD > Tester configuration roues (pour vérifier)

4. PARAMÈTRES AJUSTABLES (dans le code):
   - wheel_radius: Rayon des roues en cm (défaut: 30.0)
   - steering_factor: Intensité du braquage (défaut: 2.0)
   - max_steering_angle: Braquage maximum en degrés (défaut: 45.0)

5. RÉSULTAT:
   - Rotation pitch des roues basée sur la distance parcourue
   - Rotation yaw des freins basée sur le changement de direction
   - Clés d'animation ajoutées aux tracks existants

NOTES:
- Le script fonctionne avec les tracks de sequencer existants
- Il ajoute des clés d'animation, ne remplace pas les tracks
- Utilisez "Tester configuration" pour valider avant d'animer
"""
    unreal.log(instructions)
    print(instructions)  # Pour l'exécution en dehors d'Unreal

# For direct execution
if __name__ == "__main__":
    show_usage_instructions()
    # Décommentez la ligne suivante pour exécuter directement l'animation:
    # wheel_bake()