import unreal
import math

def get_parent(obj):
    """
    Get the parent of an object in Unreal Engine.
    """
    parent_component = unreal.EditorLevelLibrary.get_actor_parent_component(obj)
    if parent_component:
        return parent_component.get_owner()
    return None

def wheel_bake():
    """
    Animate car wheels based on movement.
    """
    try:
        # Get selected actors
        selected_actors = unreal.EditorLevelLibrary.get_selected_level_actors()
        if not selected_actors:
            unreal.log_warning("Aucun acteur sélectionné. Veuillez sélectionner les roues ou le véhicule.")
            return 0
        
        # Get the current sequencer
        sequencer = unreal.LevelSequenceEditorBlueprintLibrary.get_current_level_sequence()
        if not sequencer:
            unreal.log_warning("Aucun séquenceur n'est ouvert. Ouvrez un séquenceur et réessayez.")
            return 0
        
        # Get animation range
        start_frame = sequencer.get_playback_start()
        end_frame = sequencer.get_playback_end()
        frame_rate = sequencer.get_display_rate()
        current_time = sequencer.get_playback_position()
        
        # Find wheel actors
        wheels = []
        for actor in selected_actors:
            if "roue_" in actor.get_name().lower() or "wheel_" in actor.get_name().lower():
                wheels.append(actor)
        
        if not wheels:
            unreal.log_warning("Aucune roue trouvée. Sélectionnez des acteurs contenant 'roue_' ou 'wheel_' dans leur nom.")
            return 0
        
        # Get the car actor (parent of wheels)
        car_actor = get_parent(wheels[0])
        if not car_actor:
            unreal.log_warning("Impossible de trouver le véhicule parent. Assurez-vous que les roues sont bien attachées au véhicule.")
            return 0
        
        # Animation parameters
        wheel_radius = 30.0  # Ajustez selon la taille de vos roues
        wheel_circumference = 2 * math.pi * wheel_radius
        previous_position = car_actor.get_actor_location()
        
        # Animation loop
        for frame in range(int(start_frame), int(end_frame) + 1):
            time = frame / frame_rate
            sequencer.set_playback_position(time)
            
            # Calculate movement
            current_position = car_actor.get_actor_location()
            movement = (current_position - previous_position).length()
            previous_position = current_position
            
            # Calculate wheel rotation
            rotation_degrees = (movement / wheel_circumference) * 360.0
            
            # Apply rotation to each wheel
            for wheel in wheels:
                current_rot = wheel.get_actor_rotation()
                new_rot = unreal.Rotator(
                    current_rot.pitch,
                    current_rot.yaw,
                    current_rot.roll + rotation_degrees
                )
                wheel.set_actor_rotation(new_rot, False)
        
        # Restore original time
        sequencer.set_playback_position(current_time)
        unreal.log("Animation des roues terminée avec succès !")
        return 1
        
    except Exception as e:
        unreal.log_error(f"Erreur lors de l'animation des roues : {str(e)}")
        return 0

def register_tools(menu):
    """Register wheel animation tools in the menu"""
    section_name = "WheelAnimationSection"
    section_label = "Animation Roues"
    
    # Créer directement une nouvelle section (l'API d'Unreal Engine 5.6 gère les sections dupliquées)
    menu.add_section(section_name, section_label)
    
    # Create menu entry
    entry = unreal.ToolMenuEntry(
        name="WheelBake",
        type=unreal.MultiBlockType.MENU_ENTRY
    )
    entry.set_label("Animer les roues")
    entry.set_tool_tip("Génère l'animation des roues basée sur le déplacement du véhicule")
    entry.set_icon("EditorStyle", "Matinee.Pan.Translate")
    entry.set_string_command(
        unreal.ToolMenuStringCommandType.PYTHON,
        "Script",
        "from wheel_animation_unreal import wheel_bake; wheel_bake()"
    )
    
    # Ajouter l'entrée directement au menu en spécifiant la section
    menu.add_menu_entry(section_name, entry)
    unreal.log("Entrée de menu 'Animation des Roues' ajoutée")

# For direct execution
if __name__ == "__main__":
    wheel_bake()