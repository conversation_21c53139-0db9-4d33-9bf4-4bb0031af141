"""
Script d'animation automatique des roues pour véhicules USD dans Unreal Engine

Ce script génère automatiquement l'animation des roues basée sur le déplacement du véhicule
dans un environnement USD (Universal Scene Description) avec le Stage Editor d'Unreal Engine.

Fonctionnalités:
- Animation du roulement des roues (rotation pitch) basée sur la distance parcourue
- Animation du braquage des freins avant (rotation yaw) basée sur le changement de direction
- Support de la hiérarchie USD avec roots "heros" ou "fantome#"
- Intégration avec les tracks de sequencer existants
- Validation automatique de la configuration

Utilisation:
1. Ouvrir un séquenceur avec des tracks pour les roues et le véhicule
2. Sélectionner les tracks suivants dans le sequencer:
   - Tracks des roues: Roue_AV_G, Roue_AV_D, Roue_AR_G, Roue_AR_D
   - Tracks des freins (optionnel): Frein_AV_G, Frein_AV_D
   - Track de mouvement: heros, fantome#, ou Deplacement
3. Exécuter le script via le menu ou directement

Paramètres ajustables:
- wheel_radius: Rayon des roues en cm (défaut: 30.0)
- steering_factor: Facteur de braquage (défaut: 2.0)
- max_steering_angle: Angle de braquage maximum en degrés (défaut: 45.0)

Auteur: Adapté pour USD à partir du script Maya original
Version: 2.0 - Support USD
"""

import unreal
import math

def get_usd_stage_actor():
    """
    Get the USD Stage Actor from the current level.
    """
    editor_actor_subsystem = unreal.get_editor_subsystem(unreal.EditorActorSubsystem)
    actors = editor_actor_subsystem.get_all_level_actors()

    for actor in actors:
        if actor.get_class().get_name() == "UsdStageActor":
            return actor
    return None



def find_car_root_from_wheel(wheel_prim_path):
    """
    Find the car root (heros or fantome#) from a wheel prim path.
    """
    # Remonter la hiérarchie pour trouver le root de la voiture
    path_parts = wheel_prim_path.split('/')

    for i, part in enumerate(path_parts):
        if part.startswith('heros') or part.startswith('fantome'):
            # Construire le chemin vers le root de la voiture
            return '/'.join(path_parts[:i+1])

    return None

def find_movement_prim(car_root_path):
    """
    Find the movement prim (either the root itself or 'Deplacement').
    """
    # D'abord essayer le root lui-même
    movement_candidates = [
        car_root_path,  # Le root directement
        f"{car_root_path}/Deplacement"  # Ou l'objet Deplacement
    ]

    return movement_candidates

def get_or_create_transform_track_for_actor(actor, sequencer):
    """
    Get or create a transform track for an actor in the sequencer.
    """
    try:
        actor_name = actor.get_name()
        clean_name = actor_name.split('_UAID_')[0] if '_UAID_' in actor_name else actor_name

        # Obtenir le movie scene
        movie_scene = sequencer.get_movie_scene()

        # Chercher un possessable existant pour cet acteur
        existing_binding = None
        try:
            possessable_count = movie_scene.get_possessable_count()
            for i in range(possessable_count):
                possessable = movie_scene.get_possessable(i)
                if possessable.get_name() == clean_name:
                    existing_binding = possessable
                    break
        except:
            # Si l'API get_possessable_count n'existe pas, on va créer directement
            pass

        if existing_binding:
            unreal.log(f"Binding existant trouvé pour {clean_name}")
            # Chercher un track de transformation existant
            for track in existing_binding.get_child_tracks():
                if track.get_class().get_name() == "MovieSceneTransformTrack":
                    unreal.log(f"Track Transform existant trouvé pour {clean_name}")
                    return track

            # Créer un nouveau track de transformation
            transform_track = existing_binding.add_track(unreal.MovieSceneTransformTrack)
            unreal.log(f"Nouveau track Transform créé pour {clean_name}")
        else:
            # Créer un nouveau binding et track
            unreal.log(f"Création d'un nouveau binding pour {clean_name}")
            # Cette partie nécessite une API spécifique pour créer des bindings
            # Pour l'instant, on va retourner None et gérer autrement
            return None

        # Créer une section par défaut si elle n'existe pas
        if transform_track and not transform_track.get_sections():
            section = transform_track.add_section()
            section.set_start_frame(sequencer.get_playback_start())
            section.set_end_frame(sequencer.get_playback_end())

        return transform_track

    except Exception as e:
        unreal.log_error(f"Erreur lors de la création du track Transform pour {actor.get_name()}: {e}")
        return None

def get_rotation_channel(transform_track, channel_name):
    """
    Get a specific rotation channel (Roll, Pitch, Yaw) from a transform track.
    channel_name should be 'Roll', 'Pitch', or 'Yaw'
    """
    try:
        sections = transform_track.get_sections()
        if not sections:
            unreal.log_error(f"Aucune section trouvée dans le track Transform")
            return None

        section = sections[0]

        # Utiliser get_channels_by_type pour obtenir les canaux Float spécifiquement
        float_channels = section.get_channels_by_type(unreal.MovieSceneScriptingFloatChannel)

        if len(float_channels) < 6:
            unreal.log_error(f"Pas assez de canaux Float trouvés: {len(float_channels)}, 6 requis")
            return None

        # Les canaux sont dans l'ordre: [0,1,2] = Translation X,Y,Z, [3,4,5] = Rotation X,Y,Z
        # Rotation X = Roll, Y = Pitch, Z = Yaw
        channel_map = {
            'Roll': 3,   # Rotation X
            'Pitch': 4,  # Rotation Y
            'Yaw': 5     # Rotation Z
        }

        if channel_name in channel_map:
            channel_index = channel_map[channel_name]
            rotation_channel = float_channels[channel_index]
            unreal.log(f"Canal {channel_name} trouvé: {rotation_channel.get_name()}")
            return rotation_channel
        else:
            unreal.log_error(f"Canal {channel_name} non reconnu")
            return None

    except Exception as e:
        unreal.log_error(f"Erreur lors de l'accès au canal {channel_name}: {e}")
        return None

def validate_track_channels(track, required_channels=6):
    """
    Validate that a track has the required number of channels.
    """
    try:
        sections = track.get_sections()
        if not sections:
            return False

        channels = sections[0].get_all_channels()
        return len(channels) >= required_channels
    except:
        return False

def wheel_bake():
    """
    Animate car wheels based on movement in USD hierarchy.
    Works with selected tracks in Sequencer for USD prims.
    """
    try:
        # Get the current sequencer
        sequencer = unreal.LevelSequenceEditorBlueprintLibrary.get_current_level_sequence()
        if not sequencer:
            unreal.log_warning("Aucun séquenceur n'est ouvert. Ouvrez un séquenceur et réessayez.")
            return 0

        # Utiliser l'API officielle pour obtenir les bindings sélectionnés
        selected_bindings = unreal.LevelSequenceEditorBlueprintLibrary.get_selected_bindings()

        if not selected_bindings:
            unreal.log_warning("Aucun binding sélectionné dans le sequencer.")
            unreal.log("Sélectionnez les bindings des objets (Roue_AV_G, etc.) dans le sequencer.")
            return 0

        unreal.log(f"Bindings sélectionnés: {len(selected_bindings)}")

        # Afficher tous les bindings sélectionnés
        for i, binding in enumerate(selected_bindings):
            binding_name = str(binding.get_display_name())
            unreal.log(f"  [{i}] {binding_name}")

        # Get animation range
        start_frame = sequencer.get_playback_start()
        end_frame = sequencer.get_playback_end()

        # Analyze selected bindings to find wheels and brakes
        wheel_bindings = []
        brake_bindings = []

        for binding in selected_bindings:
            binding_name = str(binding.get_display_name())  # Convertir en string

            if "roue_" in binding_name.lower():
                wheel_bindings.append(binding)
                unreal.log(f"Roue détectée: {binding_name}")
            elif "frein_av_" in binding_name.lower():
                brake_bindings.append(binding)
                unreal.log(f"Frein détecté: {binding_name}")

        if not wheel_bindings and not brake_bindings:
            unreal.log_warning("Aucun binding de roue ou frein trouvé. Sélectionnez les bindings Roue_AV_G, Frein_AV_D, etc.")
            return 0

        # Pas besoin d'objet de mouvement - on calcule pour chaque roue individuellement
        unreal.log("Mode de calcul individuel par roue activé")

        # Obtenir ou créer les tracks de transformation pour les roues et freins
        wheel_data = []  # Liste de (binding, transform_track, nom)
        brake_data = []  # Liste de (binding, transform_track, nom)

        # Pour les roues
        for wheel_binding in wheel_bindings:
            # Obtenir l'acteur lié au binding via le subsystem
            ls_system = unreal.get_editor_subsystem(unreal.LevelSequenceEditorSubsystem)
            bound_objects = ls_system.get_bound_objects(wheel_binding)

            if not bound_objects:
                unreal.log_warning(f"Aucun objet lié au binding {str(wheel_binding.get_display_name())}")
                continue

            wheel_actor = bound_objects[0]
            wheel_name = str(wheel_binding.get_display_name())

            # Chercher un track Transform existant ou en créer un
            transform_track = None
            for track in wheel_binding.get_tracks():
                if track.get_class().get_name() == "MovieScene3DTransformTrack":
                    transform_track = track
                    unreal.log(f"Track Transform existant trouvé pour {wheel_name}")
                    break

            if not transform_track:
                # Créer un nouveau track Transform
                transform_track = wheel_binding.add_track(unreal.MovieScene3DTransformTrack)
                transform_section = transform_track.add_section()
                transform_section.set_range(start_frame, end_frame)
                unreal.log(f"Nouveau track Transform créé pour {wheel_name}")

            wheel_data.append((wheel_binding, wheel_actor, transform_track, wheel_name))

        # Pour les freins
        for brake_binding in brake_bindings:
            # Obtenir l'acteur lié au binding
            bound_objects = brake_binding.get_bound_objects()
            if not bound_objects:
                unreal.log_warning(f"Aucun objet lié au binding {str(brake_binding.get_display_name())}")
                continue

            brake_actor = bound_objects[0]
            brake_name = str(brake_binding.get_display_name())

            # Chercher un track Transform existant ou en créer un
            transform_track = None
            for track in brake_binding.get_tracks():
                if track.get_class().get_name() == "MovieScene3DTransformTrack":
                    transform_track = track
                    unreal.log(f"Track Transform existant trouvé pour {brake_name}")
                    break

            if not transform_track:
                # Créer un nouveau track Transform
                transform_track = brake_binding.add_track(unreal.MovieScene3DTransformTrack)
                transform_section = transform_track.add_section()
                transform_section.set_range(start_frame, end_frame)
                unreal.log(f"Nouveau track Transform créé pour {brake_name}")

            brake_data.append((brake_binding, brake_actor, transform_track, brake_name))

        if not wheel_data and not brake_data:
            unreal.log_warning("Aucun track de transformation créé.")
            return 0

        # Validate tracks have the required channels
        for binding, actor, track, name in wheel_data:
            if not validate_track_channels(track):
                unreal.log_warning(f"Le track de roue '{name}' n'a pas assez de canaux.")
                return 0

        for binding, actor, track, name in brake_data:
            if not validate_track_channels(track):
                unreal.log_warning(f"Le track de frein '{name}' n'a pas assez de canaux.")
                return 0
        
        # Animation parameters
        wheel_radius = 30.0  # Ajustez selon la taille de vos roues (en cm)
        wheel_circumference = 2 * math.pi * wheel_radius

        # Trouver les roues avant et arrière gauches pour calculer la direction du véhicule
        roue_av_g_actor = None
        roue_ar_g_actor = None

        for binding, actor, track, name in wheel_data:
            if "roue_av_g" in name.lower():
                roue_av_g_actor = actor
            elif "roue_ar_g" in name.lower():
                roue_ar_g_actor = actor

        if not roue_av_g_actor or not roue_ar_g_actor:
            unreal.log_warning("Roue_AV_G et Roue_AR_G nécessaires pour calculer la direction du véhicule.")
            unreal.log("Roues détectées:")
            for binding, actor, track, name in wheel_data:
                unreal.log(f"  - {name}")
            return 0

        unreal.log(f"Direction du véhicule: {roue_ar_g_actor.get_name()} → {roue_av_g_actor.get_name()}")

        # Stocker les positions précédentes de chaque roue
        previous_wheel_positions = {}
        wheel_rotations = {}  # Rotation accumulée pour chaque roue

        # Initialiser les rotations accumulées pour chaque roue
        for binding, actor, track, name in wheel_data:
            wheel_rotations[name] = 0.0
            previous_wheel_positions[name] = None

        # Animation loop - calculer pour chaque frame
        for frame in range(int(start_frame), int(end_frame) + 1):
            frame_number = unreal.FrameNumber(frame)

            # Calculer la direction du véhicule (vecteur AR_G vers AV_G)
            roue_ar_g_pos = roue_ar_g_actor.get_actor_location()
            roue_av_g_pos = roue_av_g_actor.get_actor_location()
            car_forward_vector = roue_av_g_pos - roue_ar_g_pos
            car_forward_normalized = car_forward_vector.get_safe_normal()

            # Pour chaque roue, calculer son déplacement individuel
            for binding, actor, track, name in wheel_data:
                current_wheel_pos = actor.get_actor_location()

                if previous_wheel_positions[name] is not None:
                    # Calculer le vecteur de déplacement de cette roue
                    movement_vector = current_wheel_pos - previous_wheel_positions[name]
                    movement_distance = movement_vector.length()

                    if movement_distance > 0.01:  # Éviter les divisions par zéro
                        # Déterminer le sens (avant/arrière) avec le produit scalaire
                        movement_normalized = movement_vector.get_safe_normal()
                        dot_product = movement_normalized.dot(car_forward_normalized)
                        direction_sign = 1.0 if dot_product >= 0 else -1.0

                        # Calculer la rotation de la roue
                        wheel_rotation_increment = (movement_distance / wheel_circumference) * 360.0 * direction_sign
                        wheel_rotations[name] += wheel_rotation_increment

                        # Appliquer la rotation au canal Pitch
                        pitch_channel = get_rotation_channel(track, 'Pitch')
                        if pitch_channel:
                            # Utiliser la méthode add_key avec les bons paramètres
                            new_key = pitch_channel.add_key(frame_number, wheel_rotations[name], 0.0, unreal.MovieSceneTimeUnit.DISPLAY_RATE)
                            unreal.log(f"Clé ajoutée pour {name} à la frame {frame}: {wheel_rotations[name]:.2f}°")
                        else:
                            unreal.log_warning(f"Impossible d'accéder au canal Pitch pour {name}")

                # Sauvegarder la position pour la frame suivante
                previous_wheel_positions[name] = current_wheel_pos

            # Calculer le braquage pour les freins avant
            if brake_data and frame > int(start_frame):
                # Calculer le changement de direction du véhicule
                # (Ici on pourrait utiliser la différence de direction entre frames)
                # Pour l'instant, on utilise une logique simplifiée

                for binding, actor, track, name in brake_data:
                    if "frein_av_" in name.lower():  # Seulement les freins avant
                        # Logique de braquage simplifiée - à améliorer selon vos besoins
                        steering_angle = 0.0  # Placeholder

                        yaw_channel = get_rotation_channel(track, 'Yaw')
                        if yaw_channel:
                            yaw_channel.add_key(frame_number, steering_angle, 0.0, unreal.MovieSceneTimeUnit.DISPLAY_RATE)
                        else:
                            unreal.log_warning(f"Impossible d'accéder au canal Yaw pour {name}")

        unreal.log("Animation des roues terminée avec succès !")
        unreal.log(f"Roues animées: {len(wheel_data)}")
        unreal.log(f"Freins animés: {len(brake_data)}")
        unreal.log(f"Frames traitées: {int(end_frame) - int(start_frame) + 1}")

        # Afficher les rotations finales de chaque roue
        for name, rotation in wheel_rotations.items():
            unreal.log(f"Rotation finale {name}: {rotation:.2f} degrés")
        return 1

    except Exception as e:
        unreal.log_error(f"Erreur lors de l'animation des roues : {str(e)}")
        import traceback
        unreal.log_error(f"Détails de l'erreur: {traceback.format_exc()}")
        return 0

def test_wheel_animation():
    """
    Test function to validate the wheel animation setup.
    """
    try:
        # Check if sequencer is open
        sequencer = unreal.LevelSequenceEditorBlueprintLibrary.get_current_level_sequence()
        if not sequencer:
            unreal.log_warning("TEST: Aucun séquenceur ouvert")
            return False

        # Check selected tracks (même méthode que vos scripts CSV)
        selected_tracks = unreal.LevelSequenceEditorBlueprintLibrary.get_selected_tracks()
        if not selected_tracks:
            unreal.log_warning("TEST: Aucun track sélectionné")
            unreal.log("Sélectionnez des tracks dans le sequencer avant de tester")
            return False

        unreal.log(f"TEST: {len(selected_tracks)} tracks sélectionnés:")
        for i, track in enumerate(selected_tracks):
            track_name = track.get_display_name()
            has_channels = validate_track_channels(track)
            unreal.log(f"  [{i}] {track_name} (Canaux valides: {has_channels})")

        # Analyze track types
        wheel_count = sum(1 for track in selected_tracks if "roue_" in track.get_display_name().lower())
        brake_count = sum(1 for track in selected_tracks if "frein_av_" in track.get_display_name().lower())
        movement_count = sum(1 for track in selected_tracks if any(name in track.get_display_name().lower() for name in ["heros", "fantome", "deplacement"]))

        unreal.log(f"TEST: Tracks détectés - Roues: {wheel_count}, Freins: {brake_count}, Mouvement: {movement_count}")

        if wheel_count == 0 and brake_count == 0:
            unreal.log_warning("TEST: Aucun track de roue ou frein détecté")
            return False

        if movement_count == 0:
            unreal.log_warning("TEST: Aucun track de mouvement détecté")
            return False

        unreal.log("TEST: Configuration valide pour l'animation des roues")
        return True

    except Exception as e:
        unreal.log_error(f"TEST: Erreur lors du test : {str(e)}")
        return False

def register_tools(menu):
    """Register wheel animation tools in the menu"""
    section_name = "WheelAnimationSection"
    section_label = "Animation Roues USD"

    # Créer directement une nouvelle section (l'API d'Unreal Engine 5.6 gère les sections dupliquées)
    menu.add_section(section_name, section_label)

    # Create main animation entry
    entry_animate = unreal.ToolMenuEntry(
        name="WheelBakeUSD",
        type=unreal.MultiBlockType.MENU_ENTRY
    )
    entry_animate.set_label("Animer les roues USD")
    entry_animate.set_tool_tip("Génère l'animation des roues basée sur le déplacement du véhicule USD. Sélectionnez les tracks des roues et du véhicule dans le sequencer.")
    entry_animate.set_icon("EditorStyle", "Matinee.Pan.Translate")
    entry_animate.set_string_command(
        unreal.ToolMenuStringCommandType.PYTHON,
        "Script",
        "from wheel_animation_unreal import wheel_bake; wheel_bake()"
    )

    # Create test entry
    entry_test = unreal.ToolMenuEntry(
        name="WheelTestUSD",
        type=unreal.MultiBlockType.MENU_ENTRY
    )
    entry_test.set_label("Tester configuration roues")
    entry_test.set_tool_tip("Teste la configuration actuelle pour l'animation des roues USD")
    entry_test.set_icon("EditorStyle", "Matinee.ToggleRecordInterpValues")
    entry_test.set_string_command(
        unreal.ToolMenuStringCommandType.PYTHON,
        "Script",
        "from wheel_animation_unreal import test_wheel_animation; test_wheel_animation()"
    )

    # Ajouter les entrées au menu
    menu.add_menu_entry(section_name, entry_animate)
    menu.add_menu_entry(section_name, entry_test)
    unreal.log("Entrées de menu 'Animation des Roues USD' ajoutées")

def show_usage_instructions():
    """
    Display usage instructions for the wheel animation script.
    """
    instructions = """
=== INSTRUCTIONS D'UTILISATION - Animation Roues USD ===

1. PRÉPARATION:
   - Ouvrir un séquenceur avec votre scène USD
   - Vérifier que votre hiérarchie contient:
     * Un root de véhicule (heros, fantome#, ou Deplacement)
     * Des roues (Roue_AV_G, Roue_AV_D, Roue_AR_G, Roue_AR_D)
     * Des freins avant (Frein_AV_G, Frein_AV_D) - optionnel

2. SÉLECTION DES TRACKS:
   - Dans le sequencer, sélectionner les tracks suivants:
     ✓ Track de mouvement du véhicule (heros/fantome/Deplacement)
     ✓ Tracks des roues que vous voulez animer
     ✓ Tracks des freins avant (pour le braquage)

3. EXÉCUTION:
   - Menu: Tools > Animation Roues USD > Animer les roues USD
   - Ou: Tools > Animation Roues USD > Tester configuration roues (pour vérifier)

4. PARAMÈTRES AJUSTABLES (dans le code):
   - wheel_radius: Rayon des roues en cm (défaut: 30.0)
   - steering_factor: Intensité du braquage (défaut: 2.0)
   - max_steering_angle: Braquage maximum en degrés (défaut: 45.0)

5. RÉSULTAT:
   - Rotation pitch des roues basée sur la distance parcourue
   - Rotation yaw des freins basée sur le changement de direction
   - Clés d'animation ajoutées aux tracks existants

NOTES:
- Le script fonctionne avec les tracks de sequencer existants
- Il ajoute des clés d'animation, ne remplace pas les tracks
- Utilisez "Tester configuration" pour valider avant d'animer
"""
    unreal.log(instructions)
    print(instructions)  # Pour l'exécution en dehors d'Unreal

# For direct execution
if __name__ == "__main__":
    # Exécuter directement l'animation des roues
    wheel_bake()