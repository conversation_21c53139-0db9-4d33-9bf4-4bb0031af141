import unreal
import sys
import importlib
import traceback
from pathlib import Path

def create_shared_menu():
    try:
        menus = unreal.ToolMenus.get()
        if not menus:
            unreal.log_error("Impossible d'accéder à ToolMenus")
            return
            
        # Trouver le menu principal
        main_menu = menus.find_menu("LevelEditor.MainMenu")
        if not main_menu:
            unreal.log_error("Menu principal non trouvé")
            return
            
        # Essayer de supprimer le menu s'il existe déjà
        try:
            menus.remove_menu("LevelEditor.MainMenu.Pagerender")
        except:
            pass
            
        # Créer le menu avec add_sub_menu comme dans le menu fonctionnel
        menu = main_menu.add_sub_menu(
            owner=main_menu.get_name(),
            name="Pagerender",
            label="Pagerender",
            section_name=""
        )
        
        # Ajouter une section pour les outils
        menu.add_section(
            section_name="PageToolsSection",
            label="Outils Pagerender"
        )
        
        # Charger les outils du dossier Tools
        tools_dir = Path(__file__).parent / "Tools"
        if tools_dir.exists() and tools_dir.is_dir():
            tools_dir_str = str(tools_dir)
            if tools_dir_str not in sys.path:
                sys.path.append(tools_dir_str)
            
            for py_file in tools_dir.glob("*.py"):
                if py_file.name != "__init__.py" and py_file.is_file():
                    module_name = py_file.stem
                    try:
                        # Essayer de recharger le module s'il est déjà chargé
                        if module_name in sys.modules:
                            module = importlib.reload(sys.modules[module_name])
                        else:
                            module = importlib.import_module(module_name)
                        
                        # Enregistrer les outils si la fonction existe
                        if hasattr(module, "register_tools"):
                            try:
                                module.register_tools(menu)
                                unreal.log(f" Outil chargé : {module_name}")
                            except Exception as e:
                                unreal.log_error(f" Erreur lors de l'enregistrement de {module_name}: {str(e)}")
                                unreal.log_error(traceback.format_exc())
                        
                    except Exception as e:
                        unreal.log_error(f" Erreur lors du chargement de {module_name}: {str(e)}")
                        unreal.log_error(traceback.format_exc())
        
        # Rafraîchir les widgets pour que le menu soit correctement affiché
        menus.refresh_all_widgets()
        unreal.log(" Menu 'Pagerender' ajouté avec succès")
            
    except Exception as e:
        unreal.log_error(f" Erreur critique dans create_shared_menu: {str(e)}")
        unreal.log_error(traceback.format_exc())

# Démarrer la création du menu
if hasattr(unreal, 'ToolMenus'):
    create_shared_menu()
else:
    unreal.log_warning("ToolMenus non disponible - le menu ne sera pas créé")
