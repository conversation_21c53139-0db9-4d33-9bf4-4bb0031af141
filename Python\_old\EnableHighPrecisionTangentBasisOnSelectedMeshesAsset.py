import unreal

# Get the selected assets in the Content Browser
selected_assets = unreal.EditorUtilityLibrary.get_selected_assets()

# Get the Static Mesh Editor Subsystem
static_mesh_editor_subsystem = unreal.get_editor_subsystem(unreal.StaticMeshEditorSubsystem)

# Iterate over the selected assets
for asset in selected_assets:
    # Check if the asset is a Static Mesh
    if isinstance(asset, unreal.StaticMesh):
        # Access the LOD build settings of the Static Mesh
        MeshSet = static_mesh_editor_subsystem.get_lod_build_settings(asset, 0)
        if MeshSet is not None:
            # Enable "Use High Precision Tangent Basis"
            MeshSet.use_high_precision_tangent_basis = True
            # Apply the changes
            static_mesh_editor_subsystem.set_lod_build_settings(asset, 0, MeshSet)
            
            # Save the asset after modification
            asset.modify()
            unreal.EditorAssetLibrary.save_loaded_asset(asset)
            unreal.log(f"Parameter 'Use High Precision Tangent Basis' enabled for {asset.get_name()}")
        else:
            unreal.log_warning(f"Unable to access the LOD build settings for {asset.get_name()}")