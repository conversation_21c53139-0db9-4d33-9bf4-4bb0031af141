import unreal
import csv

# Load the CSV file
csv_file = 'W:/24-206-Alpine-Video_Simulation_A110_R_Ulitme/Elements_Clients/Data positions voitures/sim_A110Rfictive/A110Rfictive.csv'
animation_data = []

with open(csv_file, newline='', encoding='utf-8-sig') as file:
    reader = csv.DictReader(file, delimiter=';')
    # Print the column names to debug
    print("Column names:", reader.fieldnames)
    for row in reader:
        animation_data.append({
            'time': float(row['Temps']),
            'distance': float(row['Distance sur trajectoire'])
        })

# Get the current sequencer
sequencer = unreal.LevelSequenceEditorBlueprintLibrary.get_current_level_sequence()
if not sequencer:
    raise Exception("No sequencer is currently open.")

# Get the selected transform track 
transform_section = unreal.LevelSequenceEditorBlueprintLibrary.get_selected_tracks()[0].get_sections()[0]
if not transform_section:
    raise Exception("No transform track is selected.")
channels = transform_section.get_all_channels()



for data in animation_data:
    time = data['time']
    distance = data['distance']

    # Add rotation keys (only pitch), (distance / (pi=3.14 R*2=64*)=200 cm=2m) *360
    channels[4].add_key(unreal.FrameNumber(time * 25), distance*180)


print("Animation data has been successfully imported into the sequencer.")
