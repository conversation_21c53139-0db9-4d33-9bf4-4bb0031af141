import unreal

# Get all USD Prim Actors
editor_actor_subsystem = unreal.get_editor_subsystem(unreal.EditorActorSubsystem)
actors = editor_actor_subsystem.get_all_level_actors()

for actor in actors:
    # Iterate through all components of the actor
    for component in actor.get_components_by_class(unreal.ActorComponent):
        # Check if the component is a USDPrimComponent or similar
        if component.get_class().get_name() == "USDPrimComponent":
            component.set_editor_property("render_custom_depth", True)
            component.set_editor_property("custom_depth_stencil_value", 1)  # Example stencil value