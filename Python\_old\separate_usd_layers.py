import os
import argparse
from pxr import Usd, UsdGeom, UsdShade, Sdf

def separate_usd_layers(input_usd_path):
    # Vérifier si le fichier d'entrée existe
    if not os.path.exists(input_usd_path):
        print(f"Erreur: Le fichier {input_usd_path} n'existe pas.")
        return False

    # Créer les chemins de sortie
    base_name = os.path.splitext(input_usd_path)[0]
    materials_path = f"{base_name}_materials.usda"
    geometry_path = f"{base_name}_geometry.usdc"
    root_path = f"{base_name}_composed.usda"

    print(f"Séparation du fichier: {input_usd_path}")
    print(f" - Layer matériaux: {materials_path}")
    print(f" - Layer géométrie: {geometry_path}")
    print(f" - Fichier racine: {root_path}")

    try:
        # Charger la scène USD
        stage = Usd.Stage.Open(input_usd_path)
        if not stage:
            raise Exception("Impossible d'ouvrir le fichier USD")

        # Créer un nouveau layer pour les matériaux
        materials_layer = Sdf.Layer.CreateNew(materials_path)
        materials_stage = Usd.Stage.Open(materials_layer)

        # Créer un nouveau layer pour la géométrie
        geometry_layer = Sdf.Layer.CreateNew(geometry_path)
        geometry_stage = Usd.Stage.Open(geometry_layer)

        # Dictionnaire pour stocker les assignations de matériaux
        material_assignments = {}

        # Copier les matériaux et leur assignation
        for prim in stage.Traverse():
            if prim.IsA(UsdShade.Material):
                material_path = str(prim.GetPath())
                material = UsdShade.Material.Define(materials_stage, material_path)
                
                # Copier les attributs du matériau
                for attr in prim.GetAttributes():
                    if attr.HasAuthoredValue():
                        material.GetPrim().CreateAttribute(
                            attr.GetName(), 
                            attr.GetTypeName()
                        ).Set(attr.Get())

            # Enregistrer les assignations de matériaux
            elif prim.HasAPI(UsdShade.MaterialBindingAPI):
                binding_api = UsdShade.MaterialBindingAPI(prim)
                bound_material = binding_api.ComputeBoundMaterial()[0]
                if bound_material:
                    material_assignments[str(prim.GetPath())] = bound_material.GetPath()

        # Créer les assignations de matériaux dans le layer des matériaux
        for prim_path, material_path in material_assignments.items():
            # Créer la prim dans le layer des matériaux si elle n'existe pas déjà
            prim = materials_stage.GetPrimAtPath(prim_path)
            if not prim:
                prim = materials_stage.OverridePrim(prim_path)
            
            # Appliquer le binding de matériau
            binding_api = UsdShade.MaterialBindingAPI.Apply(prim)
            material_prim = materials_stage.GetPrimAtPath(material_path)
            if material_prim:
                binding_api.Bind(UsdShade.Material(material_prim))

        # Copier la géométrie et les autres prims (sans les assignations de matériaux)
        for prim in stage.Traverse():
            if not prim.IsA(UsdShade.Material):
                prim_path = str(prim.GetPath())
                if prim_path == "/":
                    continue
                
                # Copier la prim dans le layer de géométrie
                new_prim = geometry_stage.DefinePrim(prim_path, prim.GetTypeName())
                
                # Copier les attributs (sauf les relations de matériaux)
                for attr in prim.GetAttributes():
                    if attr.HasAuthoredValue() and not attr.GetName().startswith("material:"):
                        new_prim.CreateAttribute(
                            attr.GetName(), 
                            attr.GetTypeName()
                        ).Set(attr.Get())

        # Créer le fichier racine qui référence les deux layers
        root_layer = Sdf.Layer.CreateNew(root_path)
        root_stage = Usd.Stage.Open(root_layer)
        root_layer.subLayerPaths = [materials_path, geometry_path]

        # Enregistrer tous les fichiers
        materials_stage.GetRootLayer().Save()
        geometry_stage.GetRootLayer().Save()
        root_stage.GetRootLayer().Save()

        print("Séparation terminée avec succès !")
        return True

    except Exception as e:
        print(f"Une erreur s'est produite: {str(e)}")
        # Nettoyer les fichiers partiellement créés en cas d''erreur
        for path in [materials_path, geometry_path, root_path]:
            if os.path.exists(path):
                try:
                    os.remove(path)
                except:
                    pass
        return False

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Sépare un fichier USD en deux layers: matériaux et géométrie.")
    parser.add_argument("input_usd", help="Chemin vers le fichier USD d'entrée")
    args = parser.parse_args()

    separate_usd_layers(args.input_usd)