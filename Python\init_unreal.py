"""
Initialisation des outils Python personnalisés pour Unreal Engine.
Ce fichier est automatiquement exécuté au démarrage d'Unreal Engine.
"""

try:
    # Importer les outils partagés
    import SharedTools
    
    # Afficher un message de confirmation
    print("✅ Outils partagés chargés avec succès")
    
    # Initialiser le menu si la fonction existe
    if hasattr(SharedTools, 'create_shared_menu'):
        SharedTools.create_shared_menu()
    else:
        import unreal
        unreal.log_warning("La fonction 'create_shared_menu' est introuvable dans SharedTools")
        
except Exception as e:
    import traceback
    import sys
    
    # Essayer d'importer unreal pour le logging
    try:
        import unreal
        has_unreal = True
    except ImportError:
        has_unreal = False
    
    # Préparer le message d'erreur
    error_msg = f"❌ Erreur lors du chargement des outils partagés: {str(e)}"
    
    # Journaliser l'erreur
    if has_unreal:
        unreal.log_error(error_msg)
        unreal.log_error(traceback.format_exc())
    else:
        print(error_msg)
        traceback.print_exc()
