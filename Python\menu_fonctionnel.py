import unreal

def create_test_menu():
    """
    Crée un menu test complet avec sections et entrées multiples
    """
    print("Création du menu test...")
    
    # 1. Récupérer l'interface ToolMenus
    menus = unreal.ToolMenus.get()
    
    # 2. Trouver la barre de menus principale
    main_menu = menus.find_menu("LevelEditor.MainMenu")
    if not main_menu:
        print("Erreur : Menu principal non trouvé")
        return
    
    # 3. Créer le menu principal "Test Tools"
    test_menu = main_menu.add_sub_menu(
        owner=main_menu.get_name(),
        name="TestTools",
        label="Test Tools",
        section_name=""
    )
    
    # 4. Créer la première section "Utilities"
    test_menu.add_section(
        section_name="TestUtilities", 
        label="Utilitaires de Test"
    )
    
    # 5. Ajouter des entrées à la section Utilities
    # Entrée 1 : Hello World (CORRIGÉ)
    entry_hello = unreal.ToolMenuEntry(
        name="TestHello",
        type=unreal.MultiBlockType.MENU_ENTRY
    )
    entry_hello.set_label("Hello World Test")
    entry_hello.set_tool_tip("Affiche un message de test dans la console")
    entry_hello.set_string_command(
        type=unreal.ToolMenuStringCommandType.PYTHON,
        custom_type=unreal.Name("TestHello"),  # AJOUT du paramètre manquant
        string="print('Hello from Test Menu!')"
    )
    test_menu.add_menu_entry("TestUtilities", entry_hello)
    
    # Entrée 2 : Informations sur le projet (CORRIGÉ)
    entry_info = unreal.ToolMenuEntry(
        name="ProjectInfo",
        type=unreal.MultiBlockType.MENU_ENTRY
    )
    entry_info.set_label("Infos Projet")
    entry_info.set_tool_tip("Affiche les informations du projet courant")
    entry_info.set_string_command(
        type=unreal.ToolMenuStringCommandType.PYTHON,
        custom_type=unreal.Name("ProjectInfo"),  # AJOUT du paramètre manquant
        string="import unreal; print(f'Projet: {unreal.SystemLibrary.get_project_name()}')"
    )
    test_menu.add_menu_entry("TestUtilities", entry_info)
    
    # 6. Créer une deuxième section "Asset Tools"
    test_menu.add_section(
        section_name="AssetTools", 
        label="Outils Assets"
    )
    
    # Entrée 3 : Compter les assets sélectionnés (CORRIGÉ)
    entry_count = unreal.ToolMenuEntry(
        name="CountSelectedAssets",
        type=unreal.MultiBlockType.MENU_ENTRY
    )
    entry_count.set_label("Compter Assets Sélectionnés")
    entry_count.set_tool_tip("Compte le nombre d'assets sélectionnés")
    entry_count.set_string_command(
        type=unreal.ToolMenuStringCommandType.PYTHON,
        custom_type=unreal.Name("CountSelectedAssets"),  # AJOUT du paramètre manquant
        string="""
import unreal
selected = unreal.EditorUtilityLibrary.get_selected_assets()
print(f'Nombre d\\'assets sélectionnés: {len(selected)}')
for asset in selected:
    print(f'- {asset.get_name()}')
"""
    )
    test_menu.add_menu_entry("AssetTools", entry_count)
    
    # 7. Créer un sous-menu "Outils Avancés"
    advanced_menu = test_menu.add_sub_menu(
        owner=test_menu.get_name(),
        name="AdvancedTools",
        label="Outils Avancés",
        section_name="AssetTools"
    )
    
    # Entrée dans le sous-menu (CORRIGÉ)
    entry_advanced = unreal.ToolMenuEntry(
        name="MemoryUsage",
        type=unreal.MultiBlockType.MENU_ENTRY
    )
    entry_advanced.set_label("Utilisation Mémoire")
    entry_advanced.set_tool_tip("Affiche l'utilisation mémoire actuelle")
    entry_advanced.set_string_command(
        type=unreal.ToolMenuStringCommandType.PYTHON,
        custom_type=unreal.Name("MemoryUsage"),  # AJOUT du paramètre manquant
        string="""
import unreal
print('Informations système:')
print(f'Nom du projet: {unreal.SystemLibrary.get_project_name()}')
print(f'Version UE: {unreal.SystemLibrary.get_engine_version()}')
"""
    )
    advanced_menu.add_menu_entry("", entry_advanced)
    
    # 8. Créer une troisième section avec séparateur
    test_menu.add_section(
        section_name="TestActions", 
        label="Actions de Test"
    )
    
    # Entrée de test avec callback plus complexe (CORRIGÉ)
    entry_test_action = unreal.ToolMenuEntry(
        name="ComplexTest",
        type=unreal.MultiBlockType.MENU_ENTRY
    )
    entry_test_action.set_label("Test Complexe")
    entry_test_action.set_tool_tip("Exécute une série de tests")
    entry_test_action.set_string_command(
        type=unreal.ToolMenuStringCommandType.PYTHON,
        custom_type=unreal.Name("ComplexTest"),  # AJOUT du paramètre manquant
        string="""
def run_complex_test():
    import unreal
    print('=== Début du test complexe ===')
    
    # Test 1: Vérifier le projet
    project_name = unreal.SystemLibrary.get_project_name()
    print(f'Projet actuel: {project_name}')
    
    # Test 2: Lister les assets dans le content browser
    assets = unreal.EditorAssetLibrary.list_assets('/Game/')
    print(f'Nombre total d\\'assets: {len(assets)}')
    
    # Test 3: Afficher les niveaux ouverts
    world = unreal.EditorLevelLibrary.get_editor_world()
    if world:
        actors = unreal.GameplayStatics.get_all_actors_of_class(world, unreal.Actor)
        print(f'Nombre d\\'acteurs dans le niveau: {len(actors)}')
    
    print('=== Fin du test complexe ===')

run_complex_test()
"""
    )
    test_menu.add_menu_entry("TestActions", entry_test_action)
    
    # 9. Rafraîchir l'interface utilisateur
    menus.refresh_all_widgets()
    print("Menu test créé avec succès!")

def remove_test_menu():
    """
    Supprime le menu test (utile pour le développement)
    """
    menus = unreal.ToolMenus.get()
    menus.remove_menu("LevelEditor.MainMenu.TestTools")
    menus.refresh_all_widgets()
    print("Menu test supprimé")

# Exécution du script
if __name__ == '__main__':
    create_test_menu()
