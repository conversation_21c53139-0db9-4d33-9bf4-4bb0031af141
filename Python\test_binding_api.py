"""
Test pour trouver la bonne API pour obtenir l'objet lié à un binding
"""

import unreal

def test_binding_apis():
    """Test différentes APIs pour obtenir l'objet d'un binding"""
    
    print("=" * 60)
    print("TEST DES APIs DE BINDING")
    print("=" * 60)
    
    # Obtenir les bindings sélectionnés
    selected_bindings = unreal.LevelSequenceEditorBlueprintLibrary.get_selected_bindings()
    
    if not selected_bindings:
        print("Aucun binding sélectionné")
        return
    
    print(f"Bindings sélectionnés: {len(selected_bindings)}")
    
    for i, binding in enumerate(selected_bindings):
        binding_name = str(binding.get_display_name())
        print(f"\n[{i}] Binding: {binding_name}")
        print(f"    Type: {type(binding)}")
        print(f"    Classe: {binding.get_class().get_name()}")
        
        # Lister toutes les méthodes disponibles
        print("    Méthodes disponibles:")
        methods = [method for method in dir(binding) if not method.startswith('_')]
        for method in methods[:10]:  # Limiter à 10 pour éviter le spam
            print(f"      - {method}")
        
        # Tester différentes APIs
        try:
            # API 1: get_object_template
            if hasattr(binding, 'get_object_template'):
                obj_template = binding.get_object_template()
                print(f"    get_object_template(): {obj_template}")
        except Exception as e:
            print(f"    get_object_template() - Erreur: {e}")
        
        try:
            # API 2: get_possessed_object_class
            if hasattr(binding, 'get_possessed_object_class'):
                obj_class = binding.get_possessed_object_class()
                print(f"    get_possessed_object_class(): {obj_class}")
        except Exception as e:
            print(f"    get_possessed_object_class() - Erreur: {e}")
        
        try:
            # API 3: get_id
            if hasattr(binding, 'get_id'):
                binding_id = binding.get_id()
                print(f"    get_id(): {binding_id}")
        except Exception as e:
            print(f"    get_id() - Erreur: {e}")
        
        try:
            # API 4: Via le sequencer et le binding ID
            sequencer = unreal.LevelSequenceEditorBlueprintLibrary.get_current_level_sequence()
            if sequencer and hasattr(binding, 'get_id'):
                binding_id = binding.get_id()
                # Essayer de résoudre l'objet via le sequencer
                print(f"    Tentative de résolution via sequencer...")
        except Exception as e:
            print(f"    Résolution via sequencer - Erreur: {e}")
        
        # Limiter à 3 bindings pour éviter le spam
        if i >= 2:
            break
    
    print("\n" + "=" * 60)

if __name__ == "__main__":
    test_binding_apis()
