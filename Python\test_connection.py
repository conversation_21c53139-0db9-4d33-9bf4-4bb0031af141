"""
Script de test pour vérifier la connexion entre Cursor et Unreal Engine
via l'extension "Unreal Engine Python" de Nils Soderman

Instructions:
1. Ouvrez Unreal Engine avec votre projet
2. Activez le plugin Python et Remote Execution
3. Sélectionnez quelques tracks dans le sequencer
4. Exécutez ce script depuis Cursor avec Ctrl+Shift+P > "Unreal Python: Execute"
"""

import unreal

def test_basic_connection():
    """Test de base de la connexion"""
    print("=" * 50)
    print("TEST DE CONNEXION CURSOR <-> UNREAL ENGINE")
    print("=" * 50)
    
    try:
        # Test 1: Log basique
        unreal.log("✅ Connexion établie depuis Cursor !")
        print("✅ Test 1: Log basique - OK")
        
        # Test 2: Vérifier le sequencer
        sequencer = unreal.LevelSequenceEditorBlueprintLibrary.get_current_level_sequence()
        if sequencer:
            unreal.log("✅ Sequencer détecté")
            print("✅ Test 2: Sequencer - OK")
        else:
            unreal.log_warning("⚠️ Aucun sequencer ouvert")
            print("⚠️ Test 2: Sequencer - Pas de sequencer ouvert")
        
        # Test 3: Tracks sélectionnés
        selected_tracks = unreal.LevelSequenceEditorBlueprintLibrary.get_selected_tracks()
        track_count = len(selected_tracks) if selected_tracks else 0
        
        unreal.log(f"📊 Tracks sélectionnés: {track_count}")
        print(f"📊 Test 3: {track_count} tracks sélectionnés")
        
        if selected_tracks:
            print("   Détail des tracks:")
            for i, track in enumerate(selected_tracks):
                track_name = track.get_display_name()
                unreal.log(f"   [{i}] {track_name}")
                print(f"   [{i}] {track_name}")
        
        # Test 4: Acteurs sélectionnés
        selected_actors = unreal.EditorLevelLibrary.get_selected_level_actors()
        actor_count = len(selected_actors) if selected_actors else 0
        
        unreal.log(f"🎭 Acteurs sélectionnés: {actor_count}")
        print(f"🎭 Test 4: {actor_count} acteurs sélectionnés")
        
        print("\n" + "=" * 50)
        print("RÉSUMÉ DU TEST")
        print("=" * 50)
        print(f"✅ Connexion: OK")
        print(f"📺 Sequencer: {'OK' if sequencer else 'Pas ouvert'}")
        print(f"📊 Tracks: {track_count}")
        print(f"🎭 Acteurs: {actor_count}")
        print("=" * 50)
        
        return True
        
    except Exception as e:
        unreal.log_error(f"❌ Erreur lors du test: {str(e)}")
        print(f"❌ Erreur: {str(e)}")
        return False

def test_wheel_script_requirements():
    """Test spécifique pour le script d'animation des roues"""
    print("\n" + "=" * 50)
    print("TEST POUR SCRIPT ANIMATION ROUES")
    print("=" * 50)
    
    try:
        # Vérifier les prérequis
        sequencer = unreal.LevelSequenceEditorBlueprintLibrary.get_current_level_sequence()
        if not sequencer:
            print("❌ Pas de sequencer ouvert")
            return False
        
        selected_tracks = unreal.LevelSequenceEditorBlueprintLibrary.get_selected_tracks()
        if not selected_tracks:
            print("❌ Aucun track sélectionné")
            print("💡 Sélectionnez des tracks dans le sequencer (roues, freins, véhicule)")
            return False
        
        # Analyser les tracks
        wheel_tracks = []
        brake_tracks = []
        movement_tracks = []
        
        for track in selected_tracks:
            track_name = track.get_display_name().lower()
            if "roue_" in track_name:
                wheel_tracks.append(track)
            elif "frein_" in track_name:
                brake_tracks.append(track)
            elif any(name in track_name for name in ["heros", "fantome", "deplacement"]):
                movement_tracks.append(track)
        
        print(f"🛞 Roues détectées: {len(wheel_tracks)}")
        print(f"🛑 Freins détectés: {len(brake_tracks)}")
        print(f"🚗 Mouvement détecté: {len(movement_tracks)}")
        
        # Recommandations
        if len(wheel_tracks) == 0:
            print("💡 Sélectionnez des tracks de roues (contenant 'roue_')")
        if len(movement_tracks) == 0:
            print("💡 Sélectionnez un track de mouvement (heros, fantome, deplacement)")
        
        ready = len(wheel_tracks) > 0 and len(movement_tracks) > 0
        print(f"\n{'✅' if ready else '❌'} Prêt pour l'animation: {'OUI' if ready else 'NON'}")
        
        return ready
        
    except Exception as e:
        print(f"❌ Erreur: {str(e)}")
        return False

if __name__ == "__main__":
    # Exécuter les tests
    test_basic_connection()
    test_wheel_script_requirements()
