"""
Test pour comprendre comment détecter les bindings sélectionnés dans le sequencer
"""

import unreal

def test_all_selection_methods():
    """Test toutes les méthodes de sélection possibles"""
    
    print("=" * 60)
    print("TEST DE TOUTES LES MÉTHODES DE SÉLECTION")
    print("=" * 60)
    
    # Méthode 1: Tracks sélectionnés
    try:
        selected_tracks = unreal.LevelSequenceEditorBlueprintLibrary.get_selected_tracks()
        print(f"1. get_selected_tracks(): {len(selected_tracks) if selected_tracks else 0}")
        if selected_tracks:
            for i, track in enumerate(selected_tracks):
                print(f"   [{i}] {track.get_display_name()} - {track.get_class().get_name()}")
    except Exception as e:
        print(f"1. get_selected_tracks() - Erreur: {e}")
    
    # Méthode 2: Objets sélectionnés dans l'outliner
    try:
        selected_actors = unreal.EditorLevelLibrary.get_selected_level_actors()
        print(f"2. get_selected_level_actors(): {len(selected_actors) if selected_actors else 0}")
        if selected_actors:
            for i, actor in enumerate(selected_actors):
                print(f"   [{i}] {actor.get_name()} - {actor.get_class().get_name()}")
    except Exception as e:
        print(f"2. get_selected_level_actors() - Erreur: {e}")
    
    # Méthode 3: Sequencer subsystem
    try:
        sequencer_subsystem = unreal.get_editor_subsystem(unreal.LevelSequenceEditorSubsystem)
        if sequencer_subsystem:
            print("3. LevelSequenceEditorSubsystem trouvé")
            # Essayer d'autres méthodes du subsystem
        else:
            print("3. LevelSequenceEditorSubsystem non trouvé")
    except Exception as e:
        print(f"3. LevelSequenceEditorSubsystem - Erreur: {e}")
    
    # Méthode 4: Sequencer actuel et ses possessables
    try:
        sequencer = unreal.LevelSequenceEditorBlueprintLibrary.get_current_level_sequence()
        if sequencer:
            print(f"4. Sequencer trouvé: {sequencer.get_display_name()}")
            
            # Lister tous les possessables
            possessable_count = sequencer.get_possessable_count()
            print(f"   Possessables totaux: {possessable_count}")
            
            for i in range(possessable_count):
                possessable = sequencer.get_possessable(i)
                print(f"   [{i}] Possessable: {possessable.get_name()}")
                
        else:
            print("4. Aucun sequencer trouvé")
    except Exception as e:
        print(f"4. Sequencer possessables - Erreur: {e}")
    
    print("=" * 60)

if __name__ == "__main__":
    test_all_selection_methods()
