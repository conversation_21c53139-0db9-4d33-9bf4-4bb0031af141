"""
Test pour comprendre comment accéder aux bindings sélectionnés dans le sequencer
"""

import unreal

def test_sequencer_selection():
    """Test toutes les méthodes pour accéder aux bindings sélectionnés"""
    
    print("=" * 60)
    print("TEST SÉLECTION SEQUENCER")
    print("=" * 60)
    
    # Obtenir le sequencer
    sequencer = unreal.LevelSequenceEditorBlueprintLibrary.get_current_level_sequence()
    if not sequencer:
        print("Aucun sequencer ouvert")
        return
    
    print(f"Sequencer: {sequencer}")
    
    # Méthode 1: Tracks sélectionnés (ne fonctionne pas pour les bindings)
    selected_tracks = unreal.LevelSequenceEditorBlueprintLibrary.get_selected_tracks()
    print(f"1. get_selected_tracks(): {len(selected_tracks) if selected_tracks else 0}")
    
    # Méthode 2: Essayer le subsystem
    try:
        sequencer_subsystem = unreal.get_editor_subsystem(unreal.LevelSequenceEditorSubsystem)
        print(f"2. LevelSequenceEditorSubsystem: {sequencer_subsystem}")
        
        # E<PERSON>yer d'autres méthodes du subsystem
        if hasattr(sequencer_subsystem, 'get_selected_objects'):
            selected_objects = sequencer_subsystem.get_selected_objects()
            print(f"   get_selected_objects(): {len(selected_objects) if selected_objects else 0}")
            if selected_objects:
                for i, obj in enumerate(selected_objects):
                    print(f"     [{i}] {obj}")
        
        if hasattr(sequencer_subsystem, 'get_selected_bindings'):
            selected_bindings = sequencer_subsystem.get_selected_bindings()
            print(f"   get_selected_bindings(): {len(selected_bindings) if selected_bindings else 0}")
            if selected_bindings:
                for i, binding in enumerate(selected_bindings):
                    print(f"     [{i}] {binding}")
                    
    except Exception as e:
        print(f"2. Erreur subsystem: {e}")
    
    # Méthode 3: Explorer le movie scene
    try:
        movie_scene = sequencer.get_movie_scene()
        print(f"3. MovieScene: {movie_scene}")
        
        # Lister tous les possessables
        if hasattr(movie_scene, 'get_possessable_count'):
            possessable_count = movie_scene.get_possessable_count()
            print(f"   Possessables: {possessable_count}")
            
            for i in range(min(possessable_count, 10)):  # Limiter à 10 pour éviter le spam
                possessable = movie_scene.get_possessable(i)
                print(f"     [{i}] {possessable.get_name()}")
        else:
            print("   get_possessable_count() non disponible")
            
        # Essayer d'autres méthodes
        if hasattr(movie_scene, 'get_bindings'):
            bindings = movie_scene.get_bindings()
            print(f"   get_bindings(): {len(bindings) if bindings else 0}")
            
    except Exception as e:
        print(f"3. Erreur movie scene: {e}")
    
    # Méthode 4: Utiliser les acteurs sélectionnés comme fallback
    selected_actors = unreal.EditorLevelLibrary.get_selected_level_actors()
    print(f"4. Acteurs sélectionnés: {len(selected_actors) if selected_actors else 0}")
    if selected_actors:
        for i, actor in enumerate(selected_actors[:5]):  # Limiter à 5
            clean_name = actor.get_name().split('_UAID_')[0] if '_UAID_' in actor.get_name() else actor.get_name()
            print(f"     [{i}] {clean_name}")
    
    print("=" * 60)

if __name__ == "__main__":
    test_sequencer_selection()
