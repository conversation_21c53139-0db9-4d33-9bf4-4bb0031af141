"""
Test simple pour vérifier la logique de calcul des roues
"""

import unreal
import math

def test_wheel_calculation():
    """Test la logique de calcul des roues sans créer de tracks"""
    
    print("=" * 60)
    print("TEST DE CALCUL DES ROUES")
    print("=" * 60)
    
    # Obtenir les objets sélectionnés
    selected_actors = unreal.EditorLevelLibrary.get_selected_level_actors()
    
    if not selected_actors:
        print("Aucun objet sélectionné")
        return
    
    print(f"Objets sélectionnés: {len(selected_actors)}")
    
    # Identifier les roues
    wheel_actors = []
    roue_av_g_actor = None
    roue_ar_g_actor = None
    
    for actor in selected_actors:
        actor_name = actor.get_name()
        clean_name = actor_name.split('_UAID_')[0] if '_UAID_' in actor_name else actor_name
        print(f"  - {clean_name}")
        
        if "roue_" in clean_name.lower():
            wheel_actors.append((actor, clean_name))
            
            if "roue_av_g" in clean_name.lower():
                roue_av_g_actor = actor
            elif "roue_ar_g" in clean_name.lower():
                roue_ar_g_actor = actor
    
    if not roue_av_g_actor or not roue_ar_g_actor:
        print("ERREUR: Roue_AV_G et Roue_AR_G nécessaires")
        return
    
    # Calculer la direction du véhicule
    roue_ar_g_pos = roue_ar_g_actor.get_actor_location()
    roue_av_g_pos = roue_av_g_actor.get_actor_location()
    car_forward_vector = roue_av_g_pos - roue_ar_g_pos
    car_forward_normalized = car_forward_vector.get_safe_normal()
    
    print(f"\nDirection du véhicule:")
    print(f"  AR_G: {roue_ar_g_pos}")
    print(f"  AV_G: {roue_av_g_pos}")
    print(f"  Vecteur avant: {car_forward_normalized}")
    
    # Simuler un déplacement pour tester
    print(f"\nTest de calcul de rotation:")
    
    wheel_radius = 30.0  # cm
    wheel_circumference = 2 * math.pi * wheel_radius
    
    # Simuler un mouvement de 100cm vers l'avant
    test_movement = unreal.Vector(100.0, 0.0, 0.0)  # 100cm en X
    movement_distance = test_movement.length()
    movement_normalized = test_movement.get_safe_normal()
    
    # Calculer le sens
    dot_product = movement_normalized.dot(car_forward_normalized)
    direction_sign = 1.0 if dot_product >= 0 else -1.0
    
    # Calculer la rotation
    wheel_rotation = (movement_distance / wheel_circumference) * 360.0 * direction_sign
    
    print(f"  Mouvement test: {test_movement}")
    print(f"  Distance: {movement_distance:.2f} cm")
    print(f"  Produit scalaire: {dot_product:.3f}")
    print(f"  Sens: {'Avant' if direction_sign > 0 else 'Arrière'}")
    print(f"  Rotation calculée: {wheel_rotation:.2f} degrés")
    
    print("\nTest réussi ! La logique de calcul fonctionne.")

if __name__ == "__main__":
    test_wheel_calculation()
