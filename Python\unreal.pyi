# Stub file pour autocomplétion Unreal Engine Python API
# Créé pour améliorer l'expérience de développement dans Cursor/VS Code

from typing import List, Optional, Any, Union

class Vector:
    x: float
    y: float
    z: float
    
    def __init__(self, x: float = 0.0, y: float = 0.0, z: float = 0.0) -> None: ...
    def length(self) -> float: ...
    def get_safe_normal(self, tolerance: float = 1e-8) -> 'Vector': ...
    def dot(self, other: 'Vector') -> float: ...
    def cross(self, other: 'Vector') -> 'Vector': ...
    def __add__(self, other: 'Vector') -> 'Vector': ...
    def __sub__(self, other: 'Vector') -> 'Vector': ...
    def __mul__(self, scalar: float) -> 'Vector': ...

class Rotator:
    pitch: float
    yaw: float
    roll: float
    
    def __init__(self, pitch: float = 0.0, yaw: float = 0.0, roll: float = 0.0) -> None: ...

class FrameNumber:
    def __init__(self, frame: int) -> None: ...

class Transform:
    location: Vector
    rotation: Rotator
    scale: Vector
    
    def __init__(self, location: Vector = None, rotation: Rotator = None, scale: Vector = None) -> None: ...

class Actor:
    def get_name(self) -> str: ...
    def get_actor_location(self) -> Vector: ...
    def get_actor_rotation(self) -> Rotator: ...
    def set_actor_location(self, location: Vector, sweep: bool = False) -> None: ...
    def set_actor_rotation(self, rotation: Rotator, teleport: bool = False) -> None: ...

class MovieSceneTrack:
    def get_display_name(self) -> str: ...
    def get_sections(self) -> List['MovieSceneSection']: ...

class MovieSceneSection:
    def get_all_channels(self) -> List['MovieSceneChannel']: ...

class MovieSceneChannel:
    def add_key(self, frame: FrameNumber, value: float) -> None: ...
    def evaluate_keys(self, frame: FrameNumber) -> float: ...

class LevelSequence:
    def get_playback_start(self) -> int: ...
    def get_playback_end(self) -> int: ...
    def get_display_rate(self) -> float: ...
    def get_possessable_count(self) -> int: ...
    def get_possessable(self, index: int) -> Any: ...
    def get_master_tracks(self) -> List[MovieSceneTrack]: ...

class LevelSequenceEditorBlueprintLibrary:
    @staticmethod
    def get_current_level_sequence() -> Optional[LevelSequence]: ...
    @staticmethod
    def get_selected_tracks() -> List[MovieSceneTrack]: ...
    @staticmethod
    def set_playback_position(time: float) -> None: ...

class EditorLevelLibrary:
    @staticmethod
    def get_selected_level_actors() -> List[Actor]: ...
    @staticmethod
    def select_nothing() -> None: ...
    @staticmethod
    def set_selected_level_actors(actors: List[Actor]) -> None: ...

class EditorActorSubsystem:
    def get_all_level_actors(self) -> List[Actor]: ...

class LevelSequenceEditorSubsystem:
    pass

# Fonctions globales
def log(message: str) -> None: ...
def log_warning(message: str) -> None: ...
def log_error(message: str) -> None: ...
def get_editor_subsystem(subsystem_class: type) -> Any: ...

# Enums et constantes
class ToolMenuStringCommandType:
    PYTHON: str

class MultiBlockType:
    MENU_ENTRY: str

class ToolMenuEntry:
    def __init__(self, name: str, type: str) -> None: ...
    def set_label(self, label: str) -> None: ...
    def set_tool_tip(self, tooltip: str) -> None: ...
    def set_icon(self, style: str, icon: str) -> None: ...
    def set_string_command(self, command_type: str, custom_type: str, string: str) -> None: ...
